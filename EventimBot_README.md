# EventimBot - Wiederhergestellter Quellcode

## Beschreibung
EventimBot ist ein automatisierter Ticket-Buchungsbot für verschiedene Ticket-Plattformen.

## Kontakt
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>

## Unterstützte Websites
1. eventim.de
2. eventim-light.com
3. muenchenticket.de/tickets
4. muenchenticket.net/shop

## Installation
```bash
npm install
```

## Konfiguration
1. E<PERSON><PERSON> `proxys.txt` mit einer Proxy-Liste (ein Proxy pro Zeile)
2. <PERSON><PERSON><PERSON> `proxy_auth.txt` mit Proxy-Authentifizierung:
   ```
   username:your_username
   password:your_password
   ```
3. <PERSON><PERSON>elle Event-Listen in `./events/`:
   - `eventim.de.txt`
   - `eventim-light.com.txt`
   - `muenchenticket.de_tickets.txt`
   - `muenchenticket.net_shop.txt`

## Verwendung
```bash
npm start
```

## Struktur
- `index.js` - Hauptdatei
- `handlers/` - Website-spezifische Handler
  - `eventim.js` - Eventim.de Handler
  - `eventim-light.js` - Eventim-Light.com Handler
  - `muenchenticket-de.js` - MuenchenTicket.de Handler
  - `muenchenticket-net.js` - MuenchenTicket.net Handler

## Features
- Proxy-Rotation für IP-Wechsel
- Automatische Cookie-Banner-Entfernung
- Zufällige Event-Auswahl
- Konfigurierbare Ticket-Anzahl
- Wartezeiten zwischen Events
- Error-Handling für "Access Denied"

## Hinweise
- Der Bot verwendet Puppeteer mit Stealth-Plugin
- Chrome-Browser wird aus `./browser/chrome.exe` geladen
- Alle Handler sind modular aufgebaut
