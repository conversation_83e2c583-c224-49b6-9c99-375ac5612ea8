ord_names = {
    1: b"accept",
    2: b"bind",
    3: b"closesocket",
    4: b"connect",
    5: b"getpeername",
    6: b"getsockname",
    7: b"getsockopt",
    8: b"htonl",
    9: b"htons",
    10: b"inet_addr",
    11: b"inet_ntoa",
    12: b"ioctlsocket",
    13: b"listen",
    14: b"ntohl",
    15: b"ntohs",
    16: b"recv",
    17: b"recvfrom",
    18: b"select",
    19: b"send",
    20: b"sendto",
    21: b"setsockopt",
    22: b"shutdown",
    23: b"socket",
    24: b"MigrateWinsockConfiguration",
    51: b"gethostbyaddr",
    52: b"gethostbyname",
    53: b"getprotobyname",
    54: b"getprotobynumber",
    55: b"getservbyname",
    56: b"getservbyport",
    57: b"gethostname",
    101: b"WSAAsyncSelect",
    102: b"WSAAsyncGetHostByAddr",
    103: b"WSAAsyncGetHostByName",
    104: b"WSAAsyncGetProtoByNumber",
    105: b"WSAAsyncGetProtoByName",
    106: b"WSAAsyncGetServByPort",
    107: b"WSAAsyncGetServByName",
    108: b"WSACancelAsyncRequest",
    109: b"WSASetBlockingHook",
    110: b"WSAUnhookBlockingHook",
    111: b"WSAGetLastError",
    112: b"WSASetLastError",
    113: b"WSACancelBlockingCall",
    114: b"WSAIsBlocking",
    115: b"WSAStartup",
    116: b"WSACleanup",
    151: b"__WSAFDIsSet",
    500: b"WEP",
    1000: b"WSApSetPostRoutine",
    1100: b"inet_network",
    1101: b"getnetbyname",
    1102: b"rcmd",
    1103: b"rexec",
    1104: b"rresvport",
    1105: b"sethostname",
    1106: b"dn_expand",
    1107: b"WSARecvEx",
    1108: b"s_perror",
    1109: b"GetAddressByNameA",
    1110: b"GetAddressByNameW",
    1111: b"EnumProtocolsA",
    1112: b"EnumProtocolsW",
    1113: b"GetTypeByNameA",
    1114: b"GetTypeByNameW",
    1115: b"GetNameByTypeA",
    1116: b"GetNameByTypeW",
    1117: b"SetServiceA",
    1118: b"SetServiceW",
    1119: b"GetServiceA",
    1120: b"GetServiceW",
    1130: b"NPLoadNameSpaces",
    1140: b"TransmitFile",
    1141: b"AcceptEx",
    1142: b"GetAcceptExSockaddrs",
}
