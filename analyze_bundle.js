const fs = require('fs');
const readline = require('readline');

// Pfad zur extrahierten JavaScript-Datei
// Passe diesen Pfad bei Bedarf an
const filePath = './extracted_eventimbot_source.js';

// Suchmuster (reguläre Ausdrücke oder einfache Strings)
// Du kannst diese Liste erweitern und verfeinern
const searchPatterns = [
    // Lizenz-Header und Copyright-Hinweise
    /MIT License/i,
    /Copyright (c)/i,
    /Apache License/i,
    /BSD License/i,
    /GPL/i,
    /Mozilla Public License/i,

    // Bekannte Modul-Wrapper / Bundler-Artefakte
    /\(function\(module, exports, __webpack_require__\)/i,
    /webpackBootstrap/i,
    /\b__esModule\b/,

    // Deine spezifischen require() Aufrufe
    /require\(['"]puppeteer-extra['"]\)/i,
    /require\(['"]fs['"]\)/i,
    /require\(['"]readline-sync['"]\)/i,

    // Bot-spezifische String-Literale
    /smarts8services@gmail\.com/i,
    /\+212 622 056197/,
    /\.\/proxys\.txt/,
    /\.\/proxy_auth\.txt/,
    /\.\/events\/eventim\.de\.txt/,
    /data-qa="TicketAmount"/,
    /\.event-list-content/,
    /\.quantity/,
    /\.api-pricecategorylist__pricecategoryitem/,
    // Füge hier weitere spezifische Strings oder Regex-Muster hinzu
];

async function analyzeFile(filePath, patterns) {
    if (!fs.existsSync(filePath)) {
        console.error(`Fehler: Datei nicht gefunden unter ${filePath}`);
        return;
    }

    const fileStream = fs.createReadStream(filePath);
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    let lineNumber = 0;
    console.log(`Starte Analyse von ${filePath}...\n`);

    for await (const line of rl) {
        lineNumber++;
        for (const pattern of patterns) {
            let match;
            if (typeof pattern === 'string') {
                if (line.includes(pattern)) {
                    match = pattern;
                }
            } else if (pattern instanceof RegExp) {
                if (pattern.test(line)) {
                    match = pattern.source;
                }
            }

            if (match) {
                console.log(`Zeile ${lineNumber}: Muster gefunden "${match}" -> ${line.trim()}`);
            }
        }
        if (lineNumber % 50000 === 0) {
            console.log(`... ${lineNumber} Zeilen verarbeitet ...`);
        }
    }
    console.log(`\nAnalyse abgeschlossen. ${lineNumber} Zeilen insgesamt gelesen.`);
}

analyzeFile(filePath, searchPatterns)
    .catch(err => console.error('Fehler während der Analyse:', err));
