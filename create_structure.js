#!/usr/bin/env node

const fs = require('fs');

// <PERSON><PERSON>elle Handler-Verzeichnis
if (!fs.existsSync('handlers')) {
    fs.mkdirSync('handlers');
}

// Erstelle die Hauptdatei
const mainFile = `// EventimBot - Wiederhergestellter Quellcode
// Kontakt: +212 622 056197 | <EMAIL>

const puppeteerExtra = require('puppeteer-extra').use(require('puppeteer-extra-plugin-stealth')());
const fs = require('fs');
const readlineSync = require('readline-sync');

// Globale Variablen
let proxies = [];
let proxyUsername, proxyPassword;
let errorMessages = ['HTTP ERROR', 'Access Denied'];

// Lade Konfiguration
function loadConfig() {
    // Lade Proxies
    fs.readFileSync('./proxys.txt', 'utf-8').split('\\n').forEach(proxy => proxies.push(proxy));
    
    // Lade Proxy-Authentifizierung
    const proxyAuthLines = fs.readFileSync('./proxy_auth.txt', 'utf-8').split('\\n');
    proxyUsername = proxyAuthLines[0].split(':')[1];
    proxyPassword = proxyAuthLines[1].split(':')[1];
}

// Hilfsfunktionen
async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function clickElement(page, selector) {
    const element = await page.$(selector);
    await element.click().catch(error => console.log(error));
}

async function launchBrowser(proxy) {
    return await puppeteerExtra.launch({
        'executablePath': './browser/chrome.exe',
        'headless': false,
        'defaultViewport': null,
        'ignoreHTTPSErrors': true,
        'timeout': 60000,
        'args': ['--no-first-run', '--proxy-server=' + proxy]
    });
}

// Website-Handler
const EventimHandler = require('./handlers/eventim');
const EventimLightHandler = require('./handlers/eventim-light');
const MuenchenTicketDEHandler = require('./handlers/muenchenticket-de');
const MuenchenTicketNETHandler = require('./handlers/muenchenticket-net');

async function main() {
    loadConfig();
    
    console.log(\`
    Contact Me
    ¨¨¨¨¨¨¨¨¨¨
Whatsapp : +212 622 056197 
Email    : <EMAIL>

\`);
    
    console.log(\`
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop\`);
    
    let websiteChoice = null;
    
    while (true) {
        websiteChoice = parseInt(readlineSync.question('\\nPlease enter the website number and hit enter: '));
        
        if (!isNaN(websiteChoice)) {
            switch (websiteChoice) {
                case 1:
                    await EventimHandler.run();
                    break;
                case 2:
                    await EventimLightHandler.run();
                    break;
                case 3:
                    await MuenchenTicketDEHandler.run();
                    break;
                case 4:
                    await MuenchenTicketNETHandler.run();
                    break;
                default:
                    console.log('Number not found');
                    continue;
            }
            break;
        } else {
            console.log('Enter a valid number. Please try again.');
        }
    }
}

// Exportiere Hilfsfunktionen für Handler
module.exports = {
    sleep,
    clickElement,
    launchBrowser,
    proxies,
    proxyUsername,
    proxyPassword,
    errorMessages
};

// Starte Bot wenn direkt ausgeführt
if (require.main === module) {
    main();
}
`;

// Erstelle Eventim.de Handler
const eventimHandler = `// Eventim.de Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxies, proxyUsername, proxyPassword, errorMessages } = require('../index');

async function removeCookieBanner(page) {
    try {
        await page.waitForSelector('#cmpbox', { timeout: 5000 });
        await page.evaluate(() => document.querySelector('#cmpbox').remove());
    } catch (error) {
        // Cookie-Banner nicht gefunden
    }
}

async function addTicketsEventim(page, quantity) {
    let addToCartFailed = false;
    
    await page.waitForSelector('.event-list-content', { timeout: 20 * 1000 })
        .catch(() => { throw new Error('Event-list-content not found'); });
    
    const ticketAmountElement = await page.$('[data-qa="TicketAmount"]');
    const moreTicketsButton = await page.$('[data-qa="MoreTickets"]');
    const lessTicketsButton = await page.$('[data-qa="LessTickets"]');
    
    const currentTicketAmount = await ticketAmountElement.evaluate(el => el.textContent);
    const currentTicketAmountInt = parseInt(currentTicketAmount);
    
    // Ticket-Anzahl anpassen
    if (quantity < currentTicketAmountInt) {
        for (let i = 0; i < quantity; i++) {
            await lessTicketsButton.click();
            await sleep(1/10);
        }
    } else if (quantity > currentTicketAmountInt) {
        const diff = quantity - currentTicketAmountInt;
        for (let i = 0; i < diff; i++) {
            await moreTicketsButton.click();
            await sleep(1/10);
        }
    }
    
    const finalTicketAmount = await ticketAmountElement.evaluate(el => el.textContent);
    const finalTicketAmountInt = parseInt(finalTicketAmount);
    
    if (finalTicketAmountInt < quantity) {
        console.log(\`Can't add the given quantity of tickets. Only \${finalTicketAmountInt} tickets will be added\`);
    }
    
    // Verfügbare Events finden
    const eventElements = await page.$$('.event-list-content');
    const availableEvents = [];
    
    for (let i = 0; i < eventElements.length - 1; i++) {
        const element = eventElements[i];
        const className = await element.evaluate(el => el.className);
        const isDisabled = className.includes('fast-booking-disabled');
        
        if (!isDisabled) {
            availableEvents.push(i);
        }
    }
    
    if (availableEvents.length > 0) {
        // Zufälliges Event auswählen
        const randomIndex = availableEvents[parseInt(Math.random() * availableEvents.length)];
        const selectedEvent = eventElements[randomIndex];
        
        await selectedEvent.click().catch(error => console.log(error));
        await sleep(1);
        
        console.log('Adding to cart');
        await clickElement(page, '[data-qa="AddToShoppingCart"]');
        await sleep(1);
        
        await removeCookieBanner(page);
        
        // Warte auf Bestätigung
        await page.waitForSelector('[data-qa="reservation-timer"]', { timeout: 20 * 1000 })
            .catch(() => { addToCartFailed = true; });
        
        if (addToCartFailed) {
            console.log('Add to cart failed');
        } else {
            console.log('Done.');
        }
    } else {
        console.log('Tickets can\\'t be added');
    }
    
    await sleep(1);
}

async function processEventimEvent(eventIndex, quantity, events) {
    try {
        if (eventIndex !== 0) {
            console.log('\\nNext Events =>');
        }
        
        const eventUrl = events[eventIndex];
        const randomProxy = proxies[parseInt(Math.random() * proxies.length)];
        
        const browser = await launchBrowser(randomProxy);
        const page = await browser.newPage();
        
        await page.authenticate({
            username: proxyUsername,
            password: proxyPassword
        });
        
        await page.goto(eventUrl);
        
        // Prüfe auf Fehler
        const pageContent = await page.content();
        let hasError = false;
        
        for (const errorMessage of errorMessages) {
            if (pageContent.includes(errorMessage)) {
                hasError = true;
                break;
            }
        }
        
        if (hasError) {
            console.log('Access Denied, Changing IP Address..');
            await browser.close().catch(() => null);
            await sleep(10);
            return await processEventimEvent(eventIndex, quantity, events);
        }
        
        console.log(\`\\nEvent Link: \${eventUrl}\`);
        
        await removeCookieBanner(page);
        await addTicketsEventim(page, quantity);
        
        try {
            await browser.close();
        } catch (error) {
            // Browser bereits geschlossen
        }
        
        await sleep(5);
        
    } catch (error) {
        console.log(error.message);
    }
}

async function run() {
    const events = fs.readFileSync('./events/eventim.de.txt', 'utf-8').split('\\n');
    console.log('\\nEvents List:', events);
    
    // Benutzer-Eingaben
    let ticketQuantity;
    while (true) {
        ticketQuantity = parseInt(readlineSync.question('\\nPlease enter the quantity number of tickets you would like the bot to add for each event: '));
        if (!isNaN(ticketQuantity)) break;
        console.log('The quantity should be a number. Please try again.');
    }
    
    let delayAfter10Events;
    while (true) {
        delayAfter10Events = parseInt(readlineSync.question('\\nPlease enter the delay that the bot should wait after 10 events. Delay in seconds: '));
        if (!isNaN(delayAfter10Events)) break;
        console.log('The delay should be a number. Please try again.');
    }
    
    console.log('\\nBot Started');
    
    let eventCounter = 0;
    
    while (true) {
        for (let i = 0; i < events.length; i++) {
            await processEventimEvent(i, ticketQuantity, events);
            eventCounter++;
            
            if (eventCounter === 10) {
                console.log(\`\\n10 Events reached. Waiting for \${delayAfter10Events} seconds.\\n\`);
                await sleep(delayAfter10Events);
                eventCounter = 0;
            }
        }
    }
}

module.exports = { run };
`;

// Erstelle weitere Handler (vereinfacht)
const eventimLightHandler = `// Eventim-Light.com Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxies, proxyUsername, proxyPassword, errorMessages } = require('../index');

// TODO: Implementierung aus dem deobfuskierten Code übertragen
async function run() {
    console.log('Eventim-Light Handler - Implementation needed');
}

module.exports = { run };
`;

const muenchenTicketDEHandler = `// MuenchenTicket.de Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxies, proxyUsername, proxyPassword, errorMessages } = require('../index');

// TODO: Implementierung aus dem deobfuskierten Code übertragen
async function run() {
    console.log('MuenchenTicket.de Handler - Implementation needed');
}

module.exports = { run };
`;

const muenchenTicketNETHandler = `// MuenchenTicket.net Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxies, proxyUsername, proxyPassword, errorMessages } = require('../index');

// TODO: Implementierung aus dem deobfuskierten Code übertragen
async function run() {
    console.log('MuenchenTicket.net Handler - Implementation needed');
}

module.exports = { run };
`;

// Erstelle package.json
const packageJson = {
    "name": "eventimbot",
    "version": "1.0.0",
    "description": "EventimBot - Automated ticket booking bot",
    "main": "index.js",
    "scripts": {
        "start": "node index.js",
        "test": "echo 'No tests specified' && exit 1"
    },
    "dependencies": {
        "puppeteer-extra": "^3.3.6",
        "puppeteer-extra-plugin-stealth": "^2.11.2",
        "readline-sync": "^1.4.10"
    },
    "keywords": ["eventim", "tickets", "automation", "bot"],
    "author": "<EMAIL>",
    "license": "MIT"
};

// Erstelle README
const readme = `# EventimBot - Wiederhergestellter Quellcode

## Beschreibung
EventimBot ist ein automatisierter Ticket-Buchungsbot für verschiedene Ticket-Plattformen.

## Kontakt
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>

## Unterstützte Websites
1. eventim.de
2. eventim-light.com
3. muenchenticket.de/tickets
4. muenchenticket.net/shop

## Installation
\`\`\`bash
npm install
\`\`\`

## Konfiguration
1. Erstelle \`proxys.txt\` mit einer Proxy-Liste (ein Proxy pro Zeile)
2. Erstelle \`proxy_auth.txt\` mit Proxy-Authentifizierung:
   \`\`\`
   username:your_username
   password:your_password
   \`\`\`
3. Erstelle Event-Listen in \`./events/\`:
   - \`eventim.de.txt\`
   - \`eventim-light.com.txt\`
   - \`muenchenticket.de_tickets.txt\`
   - \`muenchenticket.net_shop.txt\`

## Verwendung
\`\`\`bash
npm start
\`\`\`

## Struktur
- \`index.js\` - Hauptdatei
- \`handlers/\` - Website-spezifische Handler
  - \`eventim.js\` - Eventim.de Handler
  - \`eventim-light.js\` - Eventim-Light.com Handler
  - \`muenchenticket-de.js\` - MuenchenTicket.de Handler
  - \`muenchenticket-net.js\` - MuenchenTicket.net Handler

## Features
- Proxy-Rotation für IP-Wechsel
- Automatische Cookie-Banner-Entfernung
- Zufällige Event-Auswahl
- Konfigurierbare Ticket-Anzahl
- Wartezeiten zwischen Events
- Error-Handling für "Access Denied"

## Hinweise
- Der Bot verwendet Puppeteer mit Stealth-Plugin
- Chrome-Browser wird aus \`./browser/chrome.exe\` geladen
- Alle Handler sind modular aufgebaut
`;

// Speichere alle Dateien
fs.writeFileSync('eventimbot_main.js', mainFile);
fs.writeFileSync('handlers/eventim.js', eventimHandler);
fs.writeFileSync('handlers/eventim-light.js', eventimLightHandler);
fs.writeFileSync('handlers/muenchenticket-de.js', muenchenTicketDEHandler);
fs.writeFileSync('handlers/muenchenticket-net.js', muenchenTicketNETHandler);
fs.writeFileSync('eventimbot_package.json', JSON.stringify(packageJson, null, 2));
fs.writeFileSync('EventimBot_README.md', readme);

console.log('✅ Modulare Struktur erstellt!');
console.log('');
console.log('Erstellte Dateien:');
console.log('- eventimbot_main.js (Hauptdatei)');
console.log('- handlers/eventim.js (Eventim.de Handler - VOLLSTÄNDIG)');
console.log('- handlers/eventim-light.js (Eventim-Light Handler - TODO)');
console.log('- handlers/muenchenticket-de.js (MuenchenTicket.de Handler - TODO)');
console.log('- handlers/muenchenticket-net.js (MuenchenTicket.net Handler - TODO)');
console.log('- eventimbot_package.json');
console.log('- EventimBot_README.md');
console.log('');
console.log('🎯 Status:');
console.log('✅ Vollständige Deobfuskierung abgeschlossen');
console.log('✅ Modulare Struktur erstellt');
console.log('✅ Eventim.de Handler vollständig implementiert');
console.log('⏳ Weitere Handler benötigen Implementierung aus deobfuskiertem Code');
console.log('');
console.log('📋 Nächste Schritte:');
console.log('1. Implementiere die restlichen Handler');
console.log('2. Teste die Funktionalität');
console.log('3. Erstelle die Konfigurationsdateien');
