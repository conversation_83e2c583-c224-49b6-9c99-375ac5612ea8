#!/usr/bin/env python3
import zlib
import os

def decompress_file(input_file, output_file):
    try:
        with open(input_file, 'rb') as f:
            compressed_data = f.read()
        
        decompressed = zlib.decompress(compressed_data)
        
        with open(output_file, 'wb') as f:
            f.write(decompressed)
        
        print(f"Dekomprimiert: {input_file} -> {output_file} ({len(decompressed)} bytes)")
        return True
    except Exception as e:
        print(f"Fehler bei {input_file}: {e}")
        return False

# Dekomprimiere die zlib-Dateien
files_to_decompress = [
    ('_Bot2.exe.extracted/185BBE3.zlib', '185BBE3_decompressed.bin'),
    ('_Bot2.exe.extracted/4396C01.zlib', '4396C01_decompressed.bin')
]

for input_file, output_file in files_to_decompress:
    if os.path.exists(input_file):
        decompress_file(input_file, output_file)
    else:
        print(f"Datei nicht gefunden: {input_file}")
