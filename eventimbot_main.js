// EventimBot - Wiederhergestellter Quellcode
// Kontakt: +212 622 056197 | <EMAIL>

const puppeteerExtra = require('puppeteer-extra').use(require('puppeteer-extra-plugin-stealth')());
const fs = require('fs');
const readlineSync = require('readline-sync');

// Globale Variablen
let proxies = [];
let proxyUsername, proxyPassword;
let errorMessages = ['HTTP ERROR', 'Access Denied'];

// Lade Konfiguration
function loadConfig() {
    // Lade Proxies
    fs.readFileSync('./proxys.txt', 'utf-8').split('\n').forEach(proxy => proxies.push(proxy));
    
    // Lade Proxy-Authentifizierung
    const proxyAuthLines = fs.readFileSync('./proxy_auth.txt', 'utf-8').split('\n');
    proxyUsername = proxyAuthLines[0].split(':')[1];
    proxyPassword = proxyAuthLines[1].split(':')[1];
}

// Hilfsfunktionen
async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function clickElement(page, selector) {
    const element = await page.$(selector);
    await element.click().catch(error => console.log(error));
}

async function launchBrowser(proxy) {
    return await puppeteerExtra.launch({
        'executablePath': './browser/chrome.exe',
        'headless': false,
        'defaultViewport': null,
        'ignoreHTTPSErrors': true,
        'timeout': 60000,
        'args': ['--no-first-run', '--proxy-server=' + proxy]
    });
}

// Website-Handler
const EventimHandler = require('./handlers/eventim');
const EventimLightHandler = require('./handlers/eventim-light');
const MuenchenTicketDEHandler = require('./handlers/muenchenticket-de');
const MuenchenTicketNETHandler = require('./handlers/muenchenticket-net');

async function main() {
    loadConfig();
    
    console.log(`
    Contact Me
    ¨¨¨¨¨¨¨¨¨¨
Whatsapp : +212 622 056197 
Email    : <EMAIL>

`);
    
    console.log(`
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop`);
    
    let websiteChoice = null;
    
    while (true) {
        websiteChoice = parseInt(readlineSync.question('\nPlease enter the website number and hit enter: '));
        
        if (!isNaN(websiteChoice)) {
            switch (websiteChoice) {
                case 1:
                    await EventimHandler.run();
                    break;
                case 2:
                    await EventimLightHandler.run();
                    break;
                case 3:
                    await MuenchenTicketDEHandler.run();
                    break;
                case 4:
                    await MuenchenTicketNETHandler.run();
                    break;
                default:
                    console.log('Number not found');
                    continue;
            }
            break;
        } else {
            console.log('Enter a valid number. Please try again.');
        }
    }
}

// Exportiere Hilfsfunktionen für Handler
module.exports = {
    sleep,
    clickElement,
    launchBrowser,
    proxies,
    proxyUsername,
    proxyPassword,
    errorMessages
};

// Starte Bot wenn direkt ausgeführt
if (require.main === module) {
    main();
}
