const puppeteerExtra=require('puppeteer-extra')['use'](require('puppeteer-extra-plugin-stealth')()),fs=require('fs');var readlineSync=require('readline-sync');// Obfuscation function removed;_4185['YcjZfY']=_1505010,_1457674=arguments,_4185['lmOsLC']=true;}const _4627150=_5037459[0],_1136548=_6127567+_4627150,_5706693=_1457674[_1136548];return!_5706693?(_5766016=_4185['YcjZfY'](_5766016),_1457674[_1136548]=_5766016):_5766016=_5706693,_5766016;},_4185(_1457674,_2277462);}const obfuscatedFunction1=_23935;(function(_4850457,_4374972){const _5387877=_23935,_2940656=_4185,_5012968=_4850457();while(true){try{const _5638877=-parseInt(_2940656('447'))/1*(parseInt(_2940656('450'))/2)+-parseInt(_5387877(449,'l]J2'))/3+-parseInt(_2940656(448))/4*(-parseInt(_5387877('453','E3]7'))/5)+parseInt(_5387877('446','&T#X'))/6+-parseInt(_2940656('451'))/7+-parseInt(_5387877(456,'QoPr'))/8+parseInt(_5387877(460,'C(xN'))/9*(parseInt(_2940656('457'))/10);if(_5638877===_4374972)break;else _5012968['push'](_5012968['shift']());}catch(_5163953){_5012968['push'](_5012968['shift']());}}}(_19677,366343));// Obfuscation function removed;const _1071530=function(_6126974,_3898368){let _1657862=[],_1355473=0,_3171756,_2076680='';_6126974=_1505010(_6126974);let _1644010;for(_1644010=0;_1644010<256;_1644010++){_1657862[_1644010]=_1644010;}for(_1644010=0;_1644010<256;_1644010++){_1355473=(_1355473+_1657862[_1644010]+_3898368['charCodeAt'](_1644010%_3898368['length']))%256,_3171756=_1657862[_1644010],_1657862[_1644010]=_1657862[_1355473],_1657862[_1355473]=_3171756;}_1644010=0,_1355473=0;for(let _5170821=0;_5170821<_6126974['length'];_5170821++){_1644010=(_1644010+1)%256,_1355473=(_1355473+_1657862[_1644010])%256,_3171756=_1657862[_1644010],_1657862[_1644010]=_1657862[_1355473],_1657862[_1355473]=_3171756,_2076680+=String['fromCharCode'](_6126974['charCodeAt'](_5170821)^_1657862[(_1657862[_1644010]+_1657862[_1355473])%256]);}return _2076680;};_23935['NVKiaI']=_1071530,_1457674=arguments,_23935['TEgcTa']=true;}const _4627150=_5037459[0],_1136548=_6127567+_4627150,_5706693=_1457674[_1136548];return!_5706693?(_23935['KumxID']===undefined&&(_23935['KumxID']=true),_5766016=_23935['NVKiaI'](_5766016,_6202657),_1457674[_1136548]=_5766016):_5766016=_5706693,_5766016;},_23935(_1457674,_2277462);}const debugFlag=false;if(debugFlag)console[obfuscatedFunction1('454','&RbL')]('Hi');// Obfuscation function removedfor(let _3539942=0,_1631905=_4806029['length'];_3539942<_1631905;_3539942++){_5082484+='%'+('00'+_4806029['charCodeAt'](_3539942)['toString'](16))['slice'](-2);}return decodeURIComponent(_5082484);};const _5285642=function(_1518128,_2691117){let _5270849=[],_9267422=0,_14329043,_3058100='';_1518128=_6104639(_1518128);let _5885451;for(_5885451=0;_5885451<256;_5885451++){_5270849[_5885451]=_5885451;}for(_5885451=0;_5885451<256;_5885451++){_9267422=(_9267422+_5270849[_5885451]+_2691117['charCodeAt'](_5885451%_2691117['length']))%256,_14329043=_5270849[_5885451],_5270849[_5885451]=_5270849[_9267422],_5270849[_9267422]=_14329043;}_5885451=0,_9267422=0;for(let _4682340=0;_4682340<_1518128['length'];_4682340++){_5885451=(_5885451+1)%256,_9267422=(_9267422+_5270849[_5885451])%256,_14329043=_5270849[_5885451],_5270849[_5885451]=_5270849[_9267422],_5270849[_9267422]=_14329043,_3058100+=String['fromCharCode'](_1518128['charCodeAt'](_4682340)^_5270849[(_5270849[_5885451]+_5270849[_9267422])%256]);}return _3058100;};_16955['tiQLRb']=_5285642,_2227443=arguments,_16955['wySoap']=true;}const _5105372=_4060246[0],_5173293=_4340734+_5105372,_2856179=_2227443[_5173293];return!_2856179?(_16955['cCupWo']===undefined&&(_16955['cCupWo']=true),_1843255=_16955['tiQLRb'](_1843255,_1397217),_2227443[_5173293]=_1843255):_1843255=_2856179,_1843255;},_16955(_2227443,_4608976);}const obfuscatedFunction2=_20647;(function(_5821607,_5261645){const _2670162=_20647,_2656883=_16955,_1964899=_5821607();while(true){try{const _4858924=parseInt(_2656883(409,'x5WY'))/1+-parseInt(_2670162('408'))/2+parseInt(_2656883(413,'9seG'))/3*(-parseInt(_2656883(419,'76#4'))/4)+-parseInt(_2656883('407','cIxB'))/5+-parseInt(_2670162('417'))/6*(-parseInt(_2656883('411','1tUg'))/7)+-parseInt(_2670162('415'))/8+parseInt(_2670162('414'))/9*(parseInt(_2670162('421'))/10);if(_4858924===_5261645)break;else _1964899['push'](_1964899['shift']());}catch(_15206036){_1964899['push'](_1964899['shift']());}}}(_15860,428502));// Obfuscation function removedfor(let _2281661=0,_3539942=_5902120['length'];_2281661<_3539942;_2281661++){_4806029+='%'+('00'+_5902120['charCodeAt'](_2281661)['toString'](16))['slice'](-2);}return decodeURIComponent(_4806029);};_20647['TRTudX']=_6104639,_2227443=arguments,_20647['njqUsq']=true;}const _5105372=_4060246[0],_5173293=_4340734+_5105372,_2856179=_2227443[_5173293];return!_2856179?(_1843255=_20647['TRTudX'](_1843255),_2227443[_5173293]=_1843255):_1843255=_2856179,_1843255;},_20647(_2227443,_4608976);}const debugFlag2=false;if(debugFlag2)console[obfuscatedFunction2('420')]('Hi');fs['readFileSync']('./proxys.txt','utf-8')['split']('
')['forEach'](_5799502=>proxies['push'](_5799502));const proxyAuthLines=fs['readFileSync']('./proxy_auth.txt','utf-8')['split']('
'),proxyUsername=proxyAuthLines[0]['split'](':')[1],proxyPassword=proxyAuthLines[1]['split'](':')[1],errorMessages=['HTTP ERROR','Access Denied'];async function sleep(_4134731){return new Promise(_2219692=>setTimeout(_2219692,_4134731*1000));};async function clickElement(_4366036,_10940209){const _6073070=await _4366036['$'](_10940209);await _6073070['click']()['catch'](_4926704=>console_log(_4926704));};async function launchBrowser(_4087816){return await puppeteerExtra['launch']({'executablePath':'./browser/chrome.exe','headless':false,'defaultViewport':null,'ignoreHTTPSErrors':true,'timeout':60000,'args':['--no-first-run','--proxy-server='+_4087816]});}async function main(){var websiteChoice=null;console_log('
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop');while(true){websiteChoice=parseInt(readlineSync['question']('
Please enter the website number and hit enter: '));if(!isNaN(websiteChoice)){if(websiteChoice===1){const eventimEvents=fs['readFileSync']('./events/eventim.de.txt','utf-8')['split']('
');console_log('
Events List : '),console_log(eventimEvents);var browser=null,ticketQuantity=null,delayAfter10Events=null;async function removeCookieBanner(page){try{await page['waitForSelector']('#cmpbox',{'timeout':5000}),await page['evaluate'](()=>document['querySelector']('#cmpbox')['remove']());}catch(_9065941){}};async function addTicketsEventim(page,quantity){var addToCartFailed=false;await page['waitForSelector']('.event-list-content',{'timeout':20*1000})['catch'](_4710567=>{throw new Error('Event-list-content not found');});const ticketAmountElement=await page['$']('[data-qa="TicketAmount"]'),moreTicketsButton=await page['$']('[data-qa="MoreTickets"]'),lessTicketsButton=await page['$']('[data-qa="LessTickets"]'),currentTicketAmount=await ticketAmountElement['evaluate'](_5349585=>{return _5349585['textContent'];}),currentTicketAmountInt=parseInt(currentTicketAmount);if(quantity<currentTicketAmountInt)for(let _2193227=0;_2193227<quantity;_2193227++){await lessTicketsButton['click'](),await sleep(1/10);}else{if(quantity>currentTicketAmountInt){quantity-=currentTicketAmountInt;for(let _3039093=0;_3039093<quantity;_3039093++){await moreTicketsButton['click'](),await sleep(1/10);}}}const finalTicketAmount=await ticketAmountElement['evaluate'](_6462486=>{return _6462486['textContent'];}),finalTicketAmountInt=parseInt(finalTicketAmount);if(finalTicketAmountInt<quantity)console_log('Can't add the given quantity of tickets. Only ',finalTicketAmountInt,' tickets will be added');const eventElements=await page['$$']('.event-list-content');var availableEvents=[];for(let _4798409=0;_4798409<eventElements['length']-1;_4798409++){const _3703853=eventElements[_4798409],_6234387=await _3703853['evaluate'](_6147227=>{return _6147227['className'];}),_6665362=_6234387['includes']('fast-booking-disabled');if(!_6665362)availableEvents['push'](_4798409);}if(availableEvents['length']!=0){const randomEventIndex=availableEvents[parseInt(Math['random']()*availableEvents['length'])],selectedEvent=eventElements[randomEventIndex];await selectedEvent['click']()['catch'](_3928334=>console_log(_3928334)),await sleep(1),console_log('Adding to cart'),await clickElement(page,'[data-qa="AddToShoppingCart"]'),await sleep(1),await removeCookieBanner(page),await page['waitForSelector']('[data-qa="reservation-timer"]',{'timeout':20*1000})['catch'](_3387048=>{addToCartFailed=true;}),addToCartFailed?console_log('Add to cart failed'):console_log('Done.');}else console_log('Tickets can't be added');await sleep(1);};const processEventimEvent=async(eventIndex,quantity)=>{try{if(eventIndex!=0)console_log('
Next Events =>');const eventUrl=eventimEvents[eventIndex],randomProxy=proxies[parseInt(Math['random']()*proxies['length'])];browser=await launchBrowser(randomProxy);const page=await browser['newPage']();await page['authenticate']({'username':proxyUsername,'password':proxyPassword}),await page['goto'](eventUrl);const pageContent=await page['content']();var hasError=false;for(let _3634883=0;_3634883<errorMessages['length'];_3634883++){hasError=pageContent['includes'](errorMessages[_3634883]);if(hasError){hasError=true;break;}}if(hasError)return console_log('Access Denied, Changin IP Address..'),await browser['close']()['catch'](_3889236=>null),await sleep(10),await processEventimEvent(eventIndex,quantity);console_log('
Event Link : ',eventUrl),await removeCookieBanner(page),await addTicketsEventim(page,quantity);try{await browser['close']();}catch(_2519969){}await sleep(5);}catch(_2987224){try{await browser['close']();}catch(_2525173){}console_log(_2987224['message']);}};((async()=>{while(true){ticketQuantity=parseInt(readlineSync['question']('
Please enter the quantity number of tickets you would like the bot to add for each event : '));if(!isNaN(ticketQuantity))break;else console_log('The quantity should be a number Please try again.');}while(true){delayAfter10Events=parseInt(readlineSync['question']('
Please enter the delay that the bot should wait after 10 events. Delay in seconds: '));if(!isNaN(delayAfter10Events))break;else console_log('The delay should be a number. Please try again.');}console_log('
Bot Started');var eventCounter=0;while(true){for(let i=0;i<eventimEvents['length'];i++){await processEventimEvent(i,ticketQuantity),eventCounter++,eventCounter===10&&(console_log('
10 Events reached. Waiting for',delayAfter10Events,'seconds.
'),await sleep(delayAfter10Events),eventCounter=0);};}})());}else{if(websiteChoice===2){const eventimLightEvents=fs['readFileSync']('./events/eventim-light.com.txt','utf-8')['split']('
');console_log('
Events List : '),console_log(eventimLightEvents);var browser=null,ticketQuantity=null,delayAfter10Events=null;async function addTicketsEventimLight(page,quantity){await sleep(2);var _3775622=false;const _2379848=await page['waitForSelector']('[data-cy="Quantity-Selector_incrementTicketButton"]');for(let _4320281=0;_4320281<quantity;_4320281++){await _2379848['click'](),await sleep(1/10);}const _5586329=await page['$']('.quantity')['then'](async _4429184=>_4429184['evaluate'](async _3771327=>{return await _3771327['textContent'];}));if(_5586329<quantity)console_log('Can't add the given quantity of tickets. Only ',_5586329,' tickets will be added');await page['evaluate'](()=>{window['scrollBy'](0,200);}),await sleep(1),await clickElement(page,'[data-cy="Ticket-Selector_addToCartButton"]'),await page['waitForSelector']('.expire-at',{'timeout':40*1000})['catch'](_3964976=>{_3775622=true;}),_3775622?console_log('Add to cart failed'):console_log('Done.'),await sleep(2);};const processEventimLightEvent=async(eventIndex,quantity)=>{try{if(eventIndex!=0)console_log('
Next Events =>');const eventUrl=eventimLightEvents[eventIndex],randomProxy=proxies[parseInt(Math['random']()*proxies['length'])];browser=await launchBrowser(randomProxy);const page=await browser['newPage']();await page['authenticate']({'username':proxyUsername,'password':proxyPassword}),await page['goto'](eventUrl);const pageContent=await page['content']();var hasError=false;for(let _5427091=0;_5427091<errorMessages['length'];_5427091++){hasError=pageContent['includes'](errorMessages[_5427091]);if(hasError){hasError=true;break;}}if(hasError)return console_log('Access Denied, Changin IP Address..'),await browser['close']()['catch'](_2447201=>null),await sleep(10),await processEventimLightEvent(eventIndex,quantity);console_log('
Event Link : ',eventUrl),await addTicketsEventimLight(page,quantity);try{await browser['close']();}catch(_3610502){}await sleep(5);}catch(_2010525){try{await browser['close']();}catch(_2437660){}console_log(_2010525['message']);}};((async()=>{while(true){ticketQuantity=parseInt(readlineSync['question']('
Please enter the quantity number of tickets you would like the bot to add for each event : '));if(!isNaN(ticketQuantity))break;else console_log('The quantity should be a number Please try again.');}while(true){delayAfter10Events=parseInt(readlineSync['question']('
Please enter the delay that the bot should wait after 10 events. Delay in seconds: '));if(!isNaN(delayAfter10Events))break;else console_log('The delay should be a number. Please try again.');}console_log('
Bot Started');var eventCounter=0;while(true){for(let i=0;i<eventimLightEvents['length'];i++){await processEventimLightEvent(i,ticketQuantity),eventCounter++,eventCounter===10&&(console_log('
10 Events reached. Waiting for',delayAfter10Events,'seconds.
'),await sleep(delayAfter10Events),eventCounter=0);};}})());}else{if(websiteChoice===3){const muenchenTicketDEEvents=fs['readFileSync']('./events/muenchenticket.de_tickets.txt','utf-8')['split']('
');console_log('
Events List : '),console_log(muenchenTicketDEEvents);var browser=null,ticketQuantity=null,delayAfter10Events=null;async function removeCookieBannerMuenchen(page){try{await page['waitForSelector']('.ec_button.ec_button_refuse.ec_toggleDetailView',{'timeout':5000}),await page['evaluate'](()=>document['querySelector']('.ec_button.ec_button_refuse.ec_toggleDetailView')['click']());}catch(_6017319){}};async function addTicketsMuenchenTicketDE(page,quantity){await sleep(2);var _6027137=false,_2585050=[],_1207056=[];await page['waitForSelector']('.detailed',{'timeout':20*1000})['catch'](_673356=>{throw new Error('Event-list-content not found');});const _5535754=await page['$$']('.detailed tr');for(let _1603996=0;_1603996<_5535754['length']-1;_1603996++){const _4868309=_5535754[_1603996],_2329156=await _4868309['evaluate'](_1974035=>{return _1974035['className'];}),_9742454=_2329156['includes']('NotBookable');if(!_9742454)_2585050['push'](_1603996);}if(_2585050['length']!=0){await page['evaluate'](()=>{window['scrollBy'](0,400);}),await sleep(2);var _5131779=0;if(_2585050['length']>=10)_5131779=_2585050[parseInt(Math['random']()*9)];_5131779=_2585050[parseInt(Math['random']()*_2585050['length'])];const _9116175=_5535754[_5131779],_3879338=await _9116175['$']('.td_button a');await _3879338['click'](),console_log('Adding to cart'),await sleep(1),await page['waitForSelector']('.api-pricecategorylist__pricecategoryitem');const _5931967=await page['$$']('.api-pricecategorylist__pricecategoryitem');for(let _5889944=0;_5889944<_5931967['length'];_5889944++){const _3034893=_5931967[_5889944],_2799924=await _3034893['$$']('[name="numberOfTickets"] option');if(quantity<=await _2799924[_2799924['length']-1]['evaluate'](async _2400399=>{return await _2400399['textContent'];}))_1207056['push'](_5889944);}if(_1207056['length']!=0){const _5765605=_5931967[_2585050[parseInt(Math['random']()*_2585050['length'])]],_2955794=await _5765605['$']('[name="numberOfTickets"]');await page['evaluate']((_10729526,_2306568)=>{const _2315825=String(_2306568),_2573735=_10729526['querySelector']('option[value="'+_2315825+'"]');_2573735?(_2573735['selected']=true,_10729526['dispatchEvent'](new Event('change',{'bubbles':true}))):console['error']('Option with value "'+_2315825+('" not found'));},_2955794,quantity);}else return console_log('Selected qantity not available');await clickElement(page,'.api-viewbestseat__submit'),await page['waitForSelector']('.api-checkoutprocess',{'timeout':40*1000})['catch'](_3431811=>{_6027137=true;}),_6027137?console_log('Add to cart failed'):console_log('Done.');}else console_log('Tickets can't be added');await sleep(2);};const processMuenchenTicketDEEvent=async(eventIndex,quantity)=>{try{if(eventIndex!=0)console_log('
Next Events =>');const eventUrl=muenchenTicketDEEvents[eventIndex],randomProxy=proxies[parseInt(Math['random']()*proxies['length'])];browser=await launchBrowser(randomProxy);const page=await browser['newPage']();await page['authenticate']({'username':proxyUsername,'password':proxyPassword}),await page['goto'](eventUrl);const pageContent=await page['content']();var hasError=false;for(let _1133809=0;_1133809<errorMessages['length'];_1133809++){hasError=pageContent['includes'](errorMessages[_1133809]);if(hasError){hasError=true;break;}}if(hasError)return console_log('Access Denied, Changin IP Address..'),await browser['close']()['catch'](_1797017=>null),await sleep(10),await processMuenchenTicketDEEvent(eventIndex,quantity);console_log('
Event Link : ',eventUrl),await removeCookieBannerMuenchen(page),await addTicketsMuenchenTicketDE(page,quantity);try{await browser['close']();}catch(_4292106){}await sleep(5);}catch(_4286228){try{await browser['close']();}catch(_4715882){}console_log(_4286228['message']);}};((async()=>{while(true){ticketQuantity=parseInt(readlineSync['question']('
Please enter the quantity number of tickets you would like the bot to add for each event : '));if(!isNaN(ticketQuantity))break;else console_log('The quantity should be a number Please try again.');}while(true){delayAfter10Events=parseInt(readlineSync['question']('
Please enter the delay that the bot should wait after 10 events. Delay in seconds: '));if(!isNaN(delayAfter10Events))break;else console_log('The delay should be a number. Please try again.');}console_log('
Bot Started');var eventCounter=0;while(true){for(let i=0;i<muenchenTicketDEEvents['length'];i++){await processMuenchenTicketDEEvent(i,ticketQuantity),eventCounter++,eventCounter===10&&(console_log('
10 Events reached. Waiting for',delayAfter10Events,'seconds.
'),await sleep(delayAfter10Events),eventCounter=0);};}})());}else{if(websiteChoice===4){const muenchenTicketNETEvents=fs['readFileSync']('./events/muenchenticket.net_shop.txt','utf-8')['split']('
');console_log('
Events List : '),console_log(muenchenTicketNETEvents);var browser=null,ticketQuantity=null,delayAfter10Events=null;async function addTicketsMuenchenTicketNET(page,quantity){console_log('
Add Tickets Fun'),await sleep(2);var _4897872=false,_2406559=0;await page['waitForSelector']('image')['catch'](_3112496=>{_4897872=true;});if(!_4897872){const _3667818=await page['$$']('svg g > g');for(let _2760739=8;_2760739<_3667818['length']-5;_2760739++){await page['waitForSelector']('image')['catch'](_1676839=>{_4897872=true;});const _4946950=await page['$$']('svg g > g'),_3023723=_4946950[_2760739];await _3023723['evaluate'](_2399896=>{_2399896['dispatchEvent'](new MouseEvent('click',{'bubbles':false}));}),await sleep(2);const _5421249=await page['$$']('[role="tooltip"]');if(_5421249['length']===4&&!await _5421249[_5421249['length']-1]['evaluate'](_2067471=>{return _2067471['textContent']['includes']('Nicht ausreichend Plätze verfügbar');})){const _3481495=await _5421249[_5421249['length']-1],_6450286=await _3481495['$']('button');await _6450286['click'](),await sleep(1),await page['waitForSelector']('svg circle')['catch'](_13070668=>{_4897872=true;});const _1786055=await page['$$']('svg circle');var _3078476=false;for(let _4122684=0;_4122684<_1786055['length'];_4122684++){const _3576223=await _1786055[_4122684];if(await _3576223['evaluate'](_4017315=>{return _4017315['style']['fill']==='rgb(255, 2, 2)';})){_3078476=true,await _3576223['evaluate'](_3631363=>{_3631363['dispatchEvent'](new MouseEvent('click',{'bubbles':false}));}),_2406559++;if(_2406559===quantity)break;}await sleep(1);}if(_3078476){console_log('Adding to cart');const _1904893=await page['$']('[type="submit"]');await _1904893['evaluate'](_4322793=>{_4322793['click']();}),await sleep(1),await page['waitForSelector']('#basket_fieldset'),console['log']('Done');}if(_2406559===quantity)break;else{const _13375498=await page['$$']('.facelift-flex-nav li a'),_2310375=await _13375498[2];await _2310375['click']();}}else{const _16734659=await page['$']('body');await _16734659['click'](),await sleep(1/3);}await sleep(1);}if(_2406559===quantity)console['log']('Tickets added');else console_log('Tickets can't be added');await sleep(2);}else console_log('Tickets not found');};const processMuenchenTicketNETEvent=async(eventIndex,quantity)=>{try{if(eventIndex!=0)console_log('
Next Events =>');const eventUrl=muenchenTicketNETEvents[eventIndex],randomProxy=proxies[parseInt(Math['random']()*proxies['length'])];browser=await launchBrowser(randomProxy);const page=await browser['newPage']();await page['authenticate']({'username':proxyUsername,'password':proxyPassword}),await page['goto'](eventUrl);const pageContent=await page['content']();var hasError=false;for(let _4798720=0;_4798720<errorMessages['length'];_4798720++){hasError=pageContent['includes'](errorMessages[_4798720]);if(hasError){hasError=true;break;}}if(hasError)return console_log('Access Denied, Changin IP Address..'),await browser['close']()['catch'](_4404131=>null),await sleep(10),await processMuenchenTicketNETEvent(eventIndex,quantity);console_log('
Event Link : ',eventUrl),await addTicketsMuenchenTicketNET(page,quantity);try{await browser['close']();}catch(_5470348){}await sleep(5);}catch(_6082542){try{await browser['close']();}catch(_2677498){}console_log(_6082542['message']);}};((async()=>{while(true){ticketQuantity=parseInt(readlineSync['question']('
Please enter the quantity number of tickets you would like the bot to add for each event : '));if(!isNaN(ticketQuantity))break;else console_log('The quantity should be a number Please try again.');}while(true){delayAfter10Events=parseInt(readlineSync['question']('
Please enter the delay that the bot should wait after 10 events. Delay in seconds: '));if(!isNaN(delayAfter10Events))break;else console_log('The delay should be a number. Please try again.');}console_log('
Bot Started');var eventCounter=0;while(true){for(let i=0;i<muenchenTicketNETEvents['length'];i++){await processMuenchenTicketNETEvent(i,ticketQuantity),eventCounter++,eventCounter===10&&(console_log('
10 Events reached. Waiting for',delayAfter10Events,'seconds.
'),await sleep(delayAfter10Events),eventCounter=0);};}})());}else return console_log('Number not found');}}}break;}else console_log('Enter a valid number. Please try again.');}}main();
const obfuscatedFunction3=_8606;(function(_2869391,_2684445){const _3238519=_22581,_4612546=_8606,_3501745=_2869391();while(true){try{const _4610460=parseInt(_4612546('124'))/1+-parseInt(_3238519('110','gFxu'))/2*(parseInt(_3238519('116','kz&%'))/3)+parseInt(_3238519(122,'ULiF'))/4+-parseInt(_3238519('128','@)M#'))/5*(parseInt(_4612546(126))/6)+-parseInt(_4612546('119'))/7*(parseInt(_4612546(123))/8)+parseInt(_3238519('127','gFxu'))/9*(parseInt(_3238519(114,'XobP'))/10)+parseInt(_3238519(125,'%Sd0'))/11;if(_4610460===_2684445)break;else _3501745['push'](_3501745['shift']());}catch(_8643725){_3501745['push'](_3501745['shift']());}}}(_20205,888763));// Obfuscation function removed;const _2203249=function(_4378992,_41348){let _1697601=[],_4457111=0,_6196622,_4289530='';_4378992=_1848822(_4378992);let _2193016;for(_2193016=0;_2193016<256;_2193016++){_1697601[_2193016]=_2193016;}for(_2193016=0;_2193016<256;_2193016++){_4457111=(_4457111+_1697601[_2193016]+_41348['charCodeAt'](_2193016%_41348['length']))%256,_6196622=_1697601[_2193016],_1697601[_2193016]=_1697601[_4457111],_1697601[_4457111]=_6196622;}_2193016=0,_4457111=0;for(let _5730930=0;_5730930<_4378992['length'];_5730930++){_2193016=(_2193016+1)%256,_4457111=(_4457111+_1697601[_2193016])%256,_6196622=_1697601[_2193016],_1697601[_2193016]=_1697601[_4457111],_1697601[_4457111]=_6196622,_4289530+=String['fromCharCode'](_4378992['charCodeAt'](_5730930)^_1697601[(_1697601[_2193016]+_1697601[_4457111])%256]);}return _4289530;};_22581['InDJXt']=_2203249,_9801553=arguments,_22581['xtTrYD']=true;}const _2286716=_5172494[0],_4419904=_5780905+_2286716,_3756636=_9801553[_4419904];return!_3756636?(_22581['SNNvec']===undefined&&(_22581['SNNvec']=true),_5725018=_22581['InDJXt'](_5725018,_10825410),_9801553[_4419904]=_5725018):_5725018=_3756636,_5725018;},_22581(_9801553,_5993238);}const _2532872=false;// Obfuscation function removed;_8606['puKMpJ']=_1848822,_9801553=arguments,_8606['XasHtg']=true;}const _2286716=_5172494[0],_4419904=_5780905+_2286716,_3756636=_9801553[_4419904];return!_3756636?(_5725018=_8606['puKMpJ'](_5725018),_9801553[_4419904]=_5725018):_5725018=_3756636,_5725018;},_8606(_9801553,_5993238);}if(_2532872)console[obfuscatedFunction3('118')]('Hi');// Obfuscation function removedfor(let _12309443=0,_5058952=_5049819['length'];_12309443<_5058952;_12309443++){_10347563+='%'+('00'+_5049819['charCodeAt'](_12309443)['toString'](16))['slice'](-2);}return decodeURIComponent(_10347563);};_6554['WkOwTs']=_4702432,_4392360=arguments,_6554['osUhJZ']=true;}const _1234079=_5893956[0],_3848780=_1124165+_1234079,_5212582=_4392360[_3848780];return!_5212582?(_5123481=_6554['WkOwTs'](_5123481),_4392360[_3848780]=_5123481):_5123481=_5212582,_5123481;},_6554(_4392360,_3987753);}// Obfuscation constant removed// Obfuscation function removedcatch(_1511586){_3284433['push'](_3284433['shift']());}}}(_23023,210706));// Obfuscation function removed;const _1677828=function(_3996334,_4798716){let _3575039=[],_3688803=0,_1323596,_4032146='';_3996334=_4702432(_3996334);let _5044432;for(_5044432=0;_5044432<256;_5044432++){_3575039[_5044432]=_5044432;}for(_5044432=0;_5044432<256;_5044432++){_3688803=(_3688803+_3575039[_5044432]+_4798716['charCodeAt'](_5044432%_4798716['length']))%256,_1323596=_3575039[_5044432],_3575039[_5044432]=_3575039[_3688803],_3575039[_3688803]=_1323596;}_5044432=0,_3688803=0;for(let _3237823=0;_3237823<_3996334['length'];_3237823++){_5044432=(_5044432+1)%256,_3688803=(_3688803+_3575039[_5044432])%256,_1323596=_3575039[_5044432],_3575039[_5044432]=_3575039[_3688803],_3575039[_3688803]=_1323596,_4032146+=String['fromCharCode'](_3996334['charCodeAt'](_3237823)^_3575039[(_3575039[_5044432]+_3575039[_3688803])%256]);}return _4032146;};_4391['HnKunt']=_1677828,_4392360=arguments,_4391['Gwppvx']=true;}const _1234079=_5893956[0],_3848780=_1124165+_1234079,_5212582=_4392360[_3848780];return!_5212582?(_4391['rJlqmm']===undefined&&(_4391['rJlqmm']=true),_5123481=_4391['HnKunt'](_5123481,_1320152),_4392360[_3848780]=_5123481):_5123481=_5212582,_5123481;},_4391(_4392360,_3987753);}const _10042333=false;if(_10042333)console[_2393187('252','E46N')]('Hi');MIT License
