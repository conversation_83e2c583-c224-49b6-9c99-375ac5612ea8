// Eventim.de Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxies, proxyUsername, proxyPassword, errorMessages } = require('../index');

async function removeCookieBanner(page) {
    try {
        await page.waitForSelector('#cmpbox', { timeout: 5000 });
        await page.evaluate(() => document.querySelector('#cmpbox').remove());
    } catch (error) {
        // Cookie-Banner nicht gefunden
    }
}

async function addTicketsEventim(page, quantity) {
    let addToCartFailed = false;
    
    await page.waitForSelector('.event-list-content', { timeout: 20 * 1000 })
        .catch(() => { throw new Error('Event-list-content not found'); });
    
    const ticketAmountElement = await page.$('[data-qa="TicketAmount"]');
    const moreTicketsButton = await page.$('[data-qa="MoreTickets"]');
    const lessTicketsButton = await page.$('[data-qa="LessTickets"]');
    
    const currentTicketAmount = await ticketAmountElement.evaluate(el => el.textContent);
    const currentTicketAmountInt = parseInt(currentTicketAmount);
    
    // Ticket-Anzahl anpassen
    if (quantity < currentTicketAmountInt) {
        for (let i = 0; i < quantity; i++) {
            await lessTicketsButton.click();
            await sleep(1/10);
        }
    } else if (quantity > currentTicketAmountInt) {
        const diff = quantity - currentTicketAmountInt;
        for (let i = 0; i < diff; i++) {
            await moreTicketsButton.click();
            await sleep(1/10);
        }
    }
    
    const finalTicketAmount = await ticketAmountElement.evaluate(el => el.textContent);
    const finalTicketAmountInt = parseInt(finalTicketAmount);
    
    if (finalTicketAmountInt < quantity) {
        console.log(`Can't add the given quantity of tickets. Only ${finalTicketAmountInt} tickets will be added`);
    }
    
    // Verfügbare Events finden
    const eventElements = await page.$$('.event-list-content');
    const availableEvents = [];
    
    for (let i = 0; i < eventElements.length - 1; i++) {
        const element = eventElements[i];
        const className = await element.evaluate(el => el.className);
        const isDisabled = className.includes('fast-booking-disabled');
        
        if (!isDisabled) {
            availableEvents.push(i);
        }
    }
    
    if (availableEvents.length > 0) {
        // Zufälliges Event auswählen
        const randomIndex = availableEvents[parseInt(Math.random() * availableEvents.length)];
        const selectedEvent = eventElements[randomIndex];
        
        await selectedEvent.click().catch(error => console.log(error));
        await sleep(1);
        
        console.log('Adding to cart');
        await clickElement(page, '[data-qa="AddToShoppingCart"]');
        await sleep(1);
        
        await removeCookieBanner(page);
        
        // Warte auf Bestätigung
        await page.waitForSelector('[data-qa="reservation-timer"]', { timeout: 20 * 1000 })
            .catch(() => { addToCartFailed = true; });
        
        if (addToCartFailed) {
            console.log('Add to cart failed');
        } else {
            console.log('Done.');
        }
    } else {
        console.log('Tickets can\'t be added');
    }
    
    await sleep(1);
}

async function processEventimEvent(eventIndex, quantity, events) {
    try {
        if (eventIndex !== 0) {
            console.log('\nNext Events =>');
        }
        
        const eventUrl = events[eventIndex];
        const randomProxy = proxies[parseInt(Math.random() * proxies.length)];
        
        const browser = await launchBrowser(randomProxy);
        const page = await browser.newPage();
        
        await page.authenticate({
            username: proxyUsername,
            password: proxyPassword
        });
        
        await page.goto(eventUrl);
        
        // Prüfe auf Fehler
        const pageContent = await page.content();
        let hasError = false;
        
        for (const errorMessage of errorMessages) {
            if (pageContent.includes(errorMessage)) {
                hasError = true;
                break;
            }
        }
        
        if (hasError) {
            console.log('Access Denied, Changing IP Address..');
            await browser.close().catch(() => null);
            await sleep(10);
            return await processEventimEvent(eventIndex, quantity, events);
        }
        
        console.log(`\nEvent Link: ${eventUrl}`);
        
        await removeCookieBanner(page);
        await addTicketsEventim(page, quantity);
        
        try {
            await browser.close();
        } catch (error) {
            // Browser bereits geschlossen
        }
        
        await sleep(5);
        
    } catch (error) {
        console.log(error.message);
    }
}

async function run() {
    const events = fs.readFileSync('./events/eventim.de.txt', 'utf-8').split('\n');
    console.log('\nEvents List:', events);
    
    // Benutzer-Eingaben
    let ticketQuantity;
    while (true) {
        ticketQuantity = parseInt(readlineSync.question('\nPlease enter the quantity number of tickets you would like the bot to add for each event: '));
        if (!isNaN(ticketQuantity)) break;
        console.log('The quantity should be a number. Please try again.');
    }
    
    let delayAfter10Events;
    while (true) {
        delayAfter10Events = parseInt(readlineSync.question('\nPlease enter the delay that the bot should wait after 10 events. Delay in seconds: '));
        if (!isNaN(delayAfter10Events)) break;
        console.log('The delay should be a number. Please try again.');
    }
    
    console.log('\nBot Started');
    
    let eventCounter = 0;
    
    while (true) {
        for (let i = 0; i < events.length; i++) {
            await processEventimEvent(i, ticketQuantity, events);
            eventCounter++;
            
            if (eventCounter === 10) {
                console.log(`\n10 Events reached. Waiting for ${delayAfter10Events} seconds.\n`);
                await sleep(delayAfter10Events);
                eventCounter = 0;
            }
        }
    }
}

module.exports = { run };
