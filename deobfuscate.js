#!/usr/bin/env node

const fs = require('fs');

// Lese die obfuskierte Datei
const obfuscatedCode = fs.readFileSync('extracted_eventimbot_source.js', 'utf8');

// Funktion zur Hex-Dekodierung
function decodeHexStrings(code) {
    // Ersetze Hex-kodierte Strings wie '\x70\x75\x70\x70\x65\x74\x65\x65\x72'
    return code.replace(/\\x([0-9a-fA-F]{2})/g, (match, hex) => {
        return String.fromCharCode(parseInt(hex, 16));
    });
}

// Funktion zur Unicode-Dekodierung
function decodeUnicodeStrings(code) {
    // Ersetze Unicode-Strings wie '\u00a8'
    return code.replace(/\\u([0-9a-fA-F]{4})/g, (match, unicode) => {
        return String.fromCharCode(parseInt(unicode, 16));
    });
}

// Funktion zur String-Konkatenation-Vereinfachung
function simplifyStringConcatenation(code) {
    // Vereinfache String-Konkatenationen wie '\x70\x75\x70'+'\x70\x65\x74'
    return code.replace(/'([^']*?)'\+'([^']*?)'/g, "'$1$2'");
}

// Funktion zur Variablen-Umbenennung (einfache Version)
function renameCommonVariables(code) {
    const replacements = {
        '_0x5a9780': 'puppeteerExtra',
        '_0x3eaff2': 'fs',
        '_0x2aa927': 'readlineSync',
        '_0xb5a447': 'console_log',
        '_0x419bd2': 'proxies',
        '_0x57adea': 'proxyUsername',
        '_0x1d03c5': 'proxyPassword',
        '_0x48462a': 'errorMessages'
    };
    
    let result = code;
    for (const [obfuscated, readable] of Object.entries(replacements)) {
        const regex = new RegExp(obfuscated.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        result = result.replace(regex, readable);
    }
    
    return result;
}

// Hauptfunktion zur Deobfuskierung
function deobfuscate(code) {
    console.log('Starte Deobfuskierung...');
    
    // Schritt 1: Hex-Strings dekodieren
    console.log('1. Dekodiere Hex-Strings...');
    code = decodeHexStrings(code);
    
    // Schritt 2: Unicode-Strings dekodieren
    console.log('2. Dekodiere Unicode-Strings...');
    code = decodeUnicodeStrings(code);
    
    // Schritt 3: String-Konkatenationen vereinfachen
    console.log('3. Vereinfache String-Konkatenationen...');
    for (let i = 0; i < 5; i++) { // Mehrere Durchläufe für verschachtelte Konkatenationen
        code = simplifyStringConcatenation(code);
    }
    
    // Schritt 4: Variablen umbenennen
    console.log('4. Benenne Variablen um...');
    code = renameCommonVariables(code);
    
    console.log('Deobfuskierung abgeschlossen!');
    return code;
}

// Führe Deobfuskierung durch
const deobfuscatedCode = deobfuscate(obfuscatedCode);

// Speichere das Ergebnis
fs.writeFileSync('eventimbot_deobfuscated.js', deobfuscatedCode);

console.log('Deobfuskierter Code wurde in "eventimbot_deobfuscated.js" gespeichert');

// Zeige einen Vorschau-Ausschnitt
console.log('\n=== VORSCHAU (erste 50 Zeilen) ===');
const lines = deobfuscatedCode.split('\n');
console.log(lines.slice(0, 50).join('\n'));
