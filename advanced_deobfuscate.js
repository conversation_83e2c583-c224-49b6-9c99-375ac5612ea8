#!/usr/bin/env node

const fs = require('fs');

// Lese die teilweise deobfuskierte Datei
const partiallyDeobfuscated = fs.readFileSync('eventimbot_deobfuscated.js', 'utf8');

// Erweiterte Deobfuskierung
function advancedDeobfuscate(code) {
    console.log('Starte erweiterte Deobfuskierung...');
    
    // Schritt 1: Weitere Variablen-Mappings
    const variableMappings = {
        // Hauptfunktionen
        '_0x1d9707': 'sleep',
        '_0x57ee4c': 'clickElement',
        '_0x142d70': 'launchBrowser',
        '_0x29cdd3': 'main',
        
        // Event-Handler
        '_0x5d183a': 'removeCookieBanner',
        '_0x4122d0': 'addTicketsEventim',
        '_0xc3d687': 'addTicketsEventimLight',
        '_0x2b9920': 'addTicketsMuenchenTicketDE',
        '_0x5014ca': 'addTicketsMuenchenTicketNET',
        '_0x55157f': 'removeCookieBannerMuenchen',
        
        // Event-Loop-Funktionen
        '_0x7f4296': 'processEventimEvent',
        '_0x9cdb81': 'processEventimLightEvent',
        '_0x4480c6': 'processMuenchenTicketDEEvent',
        '_0x3aa8d4': 'processMuenchenTicketNETEvent',
        
        // Variablen
        '_0x346eb5': 'browser',
        '_0x385eec': 'ticketQuantity',
        '_0x30698f': 'delayAfter10Events',
        '_0x1fb700': 'eventimEvents',
        '_0x58680e': 'eventimLightEvents',
        '_0x4ff1b9': 'muenchenTicketDEEvents',
        '_0x5ae7f7': 'muenchenTicketNETEvents',
        '_0x5d76c9': 'websiteChoice',
        '_0x219f08': 'proxyAuthLines',
        
        // Hex-Werte zu Dezimal
        '0x1': '1',
        '0x2': '2',
        '0x3': '3',
        '0x4': '4',
        '0x5': '5',
        '0xa': '10',
        '0x14': '20',
        '0x28': '40',
        '0x3e8': '1000',
        '0xea60': '60000',
        '0x1388': '5000',
        
        // Weitere obfuskierte Variablen
        '_0x4dabbd': 'obfuscatedFunction1',
        '_0x4bce94': 'obfuscatedFunction2',
        '_0x7eb61f': 'obfuscatedFunction3',
        '_0x12eb62': 'debugFlag',
        '_0xc35a65': 'debugFlag2',
        
        // DOM-Selektoren und Event-Handler Parameter
        '_0x25a6b5': 'page',
        '_0xd11e12': 'page',
        '_0x2334b6': 'quantity',
        '_0xb2fa85': 'page',
        '_0x3df256': 'quantity',
        '_0x4be1e0': 'page',
        '_0x265de1': 'quantity',
        '_0x5b37df': 'page',
        '_0x23696f': 'quantity',
        '_0x7c5367': 'page',
        
        // Event-Loop Parameter
        '_0x19ab64': 'eventIndex',
        '_0x14e3cb': 'quantity',
        '_0x583b3c': 'eventIndex',
        '_0x508ac5': 'quantity',
        '_0x5a6540': 'eventIndex',
        '_0x1ebeee': 'quantity',
        '_0x5081cc': 'eventIndex',
        '_0x446c1e': 'quantity',
        
        // Browser/Page Variablen
        '_0x48b348': 'page',
        '_0x1d1940': 'page',
        '_0x2de81d': 'page',
        '_0x41ad61': 'page',
        '_0x39b61e': 'eventUrl',
        '_0x2fa3e5': 'eventUrl',
        '_0x587c5c': 'eventUrl',
        '_0xcfa25b': 'eventUrl',
        '_0xa33bc4': 'randomProxy',
        '_0x1a4f95': 'randomProxy',
        '_0x384a65': 'randomProxy',
        '_0x16247f': 'randomProxy',
        
        // Content/Error checking
        '_0x234a38': 'pageContent',
        '_0x468e24': 'pageContent',
        '_0x17411a': 'pageContent',
        '_0x184573': 'pageContent',
        '_0x8075a4': 'hasError',
        '_0x2302e6': 'hasError',
        '_0x5a6131': 'hasError',
        '_0x223341': 'hasError',
        
        // Ticket-related variables
        '_0x36e64d': 'ticketAmountElement',
        '_0x73cd3d': 'moreTicketsButton',
        '_0x31722c': 'lessTicketsButton',
        '_0xf48d66': 'currentTicketAmount',
        '_0x1d15e8': 'currentTicketAmountInt',
        '_0x116784': 'finalTicketAmount',
        '_0x29cd47': 'finalTicketAmountInt',
        '_0x1d3fe5': 'eventElements',
        '_0x379bda': 'availableEvents',
        '_0x513623': 'randomEventIndex',
        '_0x47ef19': 'selectedEvent',
        '_0x1fb2c9': 'addToCartFailed',
        
        // Counter variables
        '_0x507358': 'eventCounter',
        '_0x4318a0': 'eventCounter',
        '_0x3c974f': 'eventCounter',
        '_0x14fb92': 'eventCounter',
        '_0x2dbce4': 'i',
        '_0x206a7f': 'i',
        '_0x17d49c': 'i',
        '_0x4cc5d3': 'i'
    };
    
    // Schritt 2: Variablen ersetzen
    console.log('1. Ersetze obfuskierte Variablen...');
    let result = code;
    for (const [obfuscated, readable] of Object.entries(variableMappings)) {
        const regex = new RegExp('\\b' + obfuscated.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'g');
        result = result.replace(regex, readable);
    }
    
    // Schritt 3: Entferne obfuskierte Funktions-Wrapper
    console.log('2. Entferne Obfuskierungs-Wrapper...');
    
    // Entferne die komplexen Obfuskierungs-Funktionen am Anfang
    result = result.replace(/function _0x[a-f0-9]+\([^}]+\}[^}]+\}[^}]+\}/g, '// Obfuscation function removed');
    result = result.replace(/const _0x[a-f0-9]+=_0x[a-f0-9]+;/g, '// Obfuscation constant removed');
    result = result.replace(/\(function\([^}]+\}[^}]+\}\)\([^)]+\);/g, '// Obfuscation IIFE removed');
    
    // Schritt 4: Vereinfache Hex-Literale
    console.log('3. Vereinfache Hex-Literale...');
    result = result.replace(/0x([0-9a-fA-F]+)/g, (match, hex) => {
        const decimal = parseInt(hex, 16);
        return decimal.toString();
    });
    
    // Schritt 5: Bereinige String-Konkatenationen
    console.log('4. Bereinige String-Konkatenationen...');
    for (let i = 0; i < 10; i++) {
        result = result.replace(/'([^']*?)'\s*\+\s*'([^']*?)'/g, "'$1$2'");
        result = result.replace(/"([^"]*?)"\s*\+\s*"([^"]*?)"/g, '"$1$2"');
    }
    
    // Schritt 6: Bereinige Array-Zugriffe
    console.log('5. Bereinige Array-Zugriffe...');
    result = result.replace(/\[0x0\]/g, '[0]');
    result = result.replace(/\[0x1\]/g, '[1]');
    result = result.replace(/\[0x2\]/g, '[2]');
    result = result.replace(/\[0x3\]/g, '[3]');
    result = result.replace(/\[0x4\]/g, '[4]');
    result = result.replace(/\[0x5\]/g, '[5]');
    
    // Schritt 7: Bereinige Operatoren
    console.log('6. Bereinige Operatoren...');
    result = result.replace(/!!\[\]/g, 'true');
    result = result.replace(/!\[\]/g, 'false');
    
    // Schritt 8: Entferne leere Zeilen und bereinige Formatierung
    console.log('7. Bereinige Formatierung...');
    result = result.replace(/^\s*\/\/.*$/gm, ''); // Entferne Kommentare
    result = result.replace(/\n\s*\n\s*\n/g, '\n\n'); // Reduziere mehrfache Leerzeilen
    
    console.log('Erweiterte Deobfuskierung abgeschlossen!');
    return result;
}

// Führe erweiterte Deobfuskierung durch
const fullyDeobfuscated = advancedDeobfuscate(partiallyDeobfuscated);

// Speichere das Ergebnis
fs.writeFileSync('eventimbot_fully_deobfuscated.js', fullyDeobfuscated);

console.log('\nVollständig deobfuskierter Code wurde in "eventimbot_fully_deobfuscated.js" gespeichert');

// Zeige Statistiken
const originalLines = partiallyDeobfuscated.split('\n').length;
const deobfuscatedLines = fullyDeobfuscated.split('\n').length;
console.log(`\nStatistiken:`);
console.log(`- Original: ${originalLines} Zeilen`);
console.log(`- Deobfuskiert: ${deobfuscatedLines} Zeilen`);

// Zeige Vorschau der ersten 100 Zeilen
console.log('\n=== VORSCHAU (erste 100 Zeilen) ===');
const lines = fullyDeobfuscated.split('\n');
console.log(lines.slice(0, 100).join('\n'));
